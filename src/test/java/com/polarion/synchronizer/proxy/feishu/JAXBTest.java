package com.polarion.synchronizer.proxy.feishu;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * JAXB测试类
 * 用于快速验证JAXB配置是否正确
 */
public class JAXBTest {
    
    public static void main(String[] args) {
        testFeishuConnection();
        testFeishuProxyConfiguration();
    }
    
    /**
     * 测试FeishuConnection的JAXB序列化/反序列化
     */
    public static void testFeishuConnection() {
        System.out.println("=== 测试 FeishuConnection ===");
        
        try {
            // 创建JAXB上下文
            JAXBContext context = JAXBContext.newInstance(FeishuConnection.class);
            
            // 创建测试对象
            FeishuConnection connection = new FeishuConnection("test", "http://feishu.com", "123", "user123");
            
            // 序列化测试
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            
            StringWriter writer = new StringWriter();
            marshaller.marshal(connection, writer);
            String xml = writer.toString();
            
            System.out.println("序列化成功:");
            System.out.println(xml);
            
            // 反序列化测试
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader reader = new StringReader(xml);
            FeishuConnection unmarshalled = (FeishuConnection) unmarshaller.unmarshal(reader);
            
            System.out.println("反序列化成功:");
            System.out.println("ID: " + unmarshalled.getId());
            System.out.println("Server URL: " + unmarshalled.getServerUrl());
            System.out.println("Plugin Token: " + unmarshalled.getPluginToken());
            System.out.println("User Key: " + unmarshalled.getUserKey());
            
        } catch (JAXBException e) {
            System.err.println("FeishuConnection JAXB错误:");
            e.printStackTrace();
        }
    }
    
    /**
     * 测试FeishuProxyConfiguration的JAXB序列化/反序列化
     */
    public static void testFeishuProxyConfiguration() {
        System.out.println("\n=== 测试 FeishuProxyConfiguration ===");
        
        try {
            // 创建JAXB上下文
            JAXBContext context = JAXBContext.newInstance(FeishuProxyConfiguration.class);
            
            // 创建测试对象
            FeishuProxyConfiguration config = new FeishuProxyConfiguration();
            config.setProjectKey("test-project");
            config.setWorkItemTypes("story,task,bug");
            config.setQuery("status=open");
            config.setSpaceId("space123");
            config.setFieldMappingConfig("title:name:TEXT,status:status:OPTION");
            
            // 序列化测试
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            
            StringWriter writer = new StringWriter();
            marshaller.marshal(config, writer);
            String xml = writer.toString();
            
            System.out.println("序列化成功:");
            System.out.println(xml);
            
            // 反序列化测试
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader reader = new StringReader(xml);
            FeishuProxyConfiguration unmarshalled = (FeishuProxyConfiguration) unmarshaller.unmarshal(reader);
            
            System.out.println("反序列化成功:");
            System.out.println("Project Key: " + unmarshalled.getProjectKey());
            System.out.println("Work Item Types: " + unmarshalled.getWorkItemTypes());
            System.out.println("Query: " + unmarshalled.getQuery());
            System.out.println("Space ID: " + unmarshalled.getSpaceId());
            System.out.println("Field Mapping Config: " + unmarshalled.getFieldMappingConfig());
            
        } catch (JAXBException e) {
            System.err.println("FeishuProxyConfiguration JAXB错误:");
            e.printStackTrace();
        }
    }
}
