/**
 * 飞书项目连接器UI组件
 * 定义前端配置界面的JavaScript组件
 */

// 飞书项目代理配置模型
App.FeishuProxyConfiguration = App.ProxyConfiguration.extend({
    resourceProperties: [
        'proxyType',
        'projectKey',
        'workItemTypes',
        'query',
        'spaceId',
        'fieldMappingConfig',
        'connection'
    ],
    type: "",
    description: function() {
        return this.connection == null ? 
                '未配置连接' :
                this.projectKey == null ? 
                        '未指定项目Key，服务器: ' + this.connection.serverUrl :
                        '项目: ' + this.projectKey + '，服务器: ' + this.connection.serverUrl;
    }.property('projectKey', 'connection'),
    name: '飞书项目配置'
});

// 飞书项目代理配置视图
App.FeishuProxyConfigurationView = Ember.View.extend({
    classNames: ["rows"],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>项目Key:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="projectKey" placeholder="输入飞书项目的project_key"}}',
        '    <span class="help-inline">飞书项目的唯一标识符</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">工作项类型:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="workItemTypes" placeholder="story,task,bug"}}',
        '    <span class="help-inline">要同步的工作项类型，多个类型用逗号分隔</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">查询条件:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextArea valueBinding="query" placeholder="输入自定义查询条件（可选）"}}',
        '    <span class="help-inline">可选的查询过滤条件</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">空间ID:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="spaceId" placeholder="输入空间ID（可选）"}}',
        '    <span class="help-inline">指定要同步的空间ID</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">字段映射配置:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextArea valueBinding="fieldMappingConfig" placeholder="polarionField:feishuField:type,例如: title:name:TEXT,status:status:OPTION"}}',
        '    <span class="help-inline">自定义字段映射，格式: polarionField:feishuField:type，多个映射用逗号分隔</span>',
        '  </div>',
        '</div>'
    ].join('\n'))
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({});

// 飞书项目连接模型
App.FeishuConnection = App.Connection.extend({
    resourceProperties: [
        'id',
        'connectionType',
        'serverUrl',
        'authMode',
        'pluginId',
        'user',
        'password'  // 根据authMode存储不同的敏感信息
    ],
    description: function() {
        var authModeText = this.get('authMode') === 'plugin' ? '插件' :
                          this.get('authMode') === 'personal' ? '个人令牌' : '用户令牌';
        return '飞书项目连接 (' + authModeText + '): ' + this.serverUrl;
    }.property('serverUrl', 'authMode')
});

// 飞书项目连接视图
App.FeishuConnectionView = Ember.View.extend({
    classNames: ['rows'],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Server URL:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.serverUrl" placeholder="https://project.feishu.cn"}}',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>认证模式:</label>',
        '  <div class="controls">',
        '    {{view Ember.Select valueBinding="controller.content.authMode" content=controller.authModeOptions optionValuePath="content.value" optionLabelPath="content.label"}}',
        '    <span class="help-inline">选择身份验证方式</span>',
        '  </div>',
        '</div>',
        '{{#if controller.isPluginMode}}',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Plugin ID:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.pluginId" placeholder="Enter plugin ID"}}',
        '    <span class="help-inline">飞书项目插件的唯一标识符</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Plugin Secret:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField type="password" valueBinding="controller.content.password" placeholder="Enter plugin secret"}}',
        '    <span class="help-inline">Secret将安全存储在保险库中</span>',
        '  </div>',
        '</div>',
        '{{/if}}',
        '{{#if controller.isPersonalMode}}',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Personal Access Token:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField type="password" valueBinding="controller.content.password" placeholder="Enter personal access token"}}',
        '    <span class="help-inline">个人访问令牌将安全存储在保险库中</span>',
        '  </div>',
        '</div>',
        '{{/if}}',
        '{{#if controller.isUserMode}}',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>User Token:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField type="password" valueBinding="controller.content.password" placeholder="Enter user token"}}',
        '    <span class="help-inline">用户令牌将安全存储在保险库中</span>',
        '  </div>',
        '</div>',
        '{{/if}}'
    ].join('\n'))
});

// 飞书项目代理配置控制器
App.FeishuProxyConfigurationController = Ember.ObjectController.extend({
    // 可以在这里添加自定义的控制器逻辑

    // 验证项目Key格式
    validateProjectKey: function() {
        var projectKey = this.get('projectKey');
        if (projectKey && projectKey.trim().length > 0) {
            // 可以添加项目Key格式验证逻辑
            return true;
        }
        return false;
    },

    // 验证字段映射配置格式
    validateFieldMapping: function() {
        var fieldMappingConfig = this.get('fieldMappingConfig');
        if (!fieldMappingConfig || fieldMappingConfig.trim().length === 0) {
            return true; // 空配置是允许的
        }

        try {
            var mappings = fieldMappingConfig.split(',');
            for (var i = 0; i < mappings.length; i++) {
                var mapping = mappings[i].trim();
                var parts = mapping.split(':');
                if (parts.length < 2) {
                    return false;
                }
            }
            return true;
        } catch (e) {
            return false;
        }
    },

    // 获取工作项类型选项
    workItemTypeOptions: [
        { value: 'story', label: 'Story' },
        { value: 'task', label: 'Task' },
        { value: 'bug', label: 'Bug' },
        { value: 'epic', label: 'Epic' }
    ],

    // 获取字段类型选项
    fieldTypeOptions: [
        { value: 'TEXT', label: '文本' },
        { value: 'NUMBER', label: '数字' },
        { value: 'OPTION', label: '选项' },
        { value: 'USER', label: '用户' },
        { value: 'DATE', label: '日期' },
        { value: 'MULTI_OPTION', label: '多选' },
        { value: 'RICH_TEXT', label: '富文本' }
    ]
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({
    // 认证模式选项
    authModeOptions: [
        { value: 'plugin', label: '插件身份凭证 (Plugin ID + Secret)' },
        { value: 'personal', label: '个人访问令牌 (Personal Access Token)' },
        { value: 'user', label: '用户身份凭证 (User Token)' }
    ],

    // 初始化默认认证模式
    init: function() {
        this._super();
        if (!this.get('content.authMode')) {
            this.set('content.authMode', 'plugin');
        }
    },

    // 计算属性：是否为插件模式
    isPluginMode: function() {
        return this.get('content.authMode') === 'plugin';
    }.property('content.authMode'),

    // 计算属性：是否为个人令牌模式
    isPersonalMode: function() {
        return this.get('content.authMode') === 'personal';
    }.property('content.authMode'),

    // 计算属性：是否为用户令牌模式
    isUserMode: function() {
        return this.get('content.authMode') === 'user';
    }.property('content.authMode')
});
