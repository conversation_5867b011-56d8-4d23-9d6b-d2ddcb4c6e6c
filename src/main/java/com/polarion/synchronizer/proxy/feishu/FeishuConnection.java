package com.polarion.synchronizer.proxy.feishu;

import com.polarion.synchronizer.spi.Connection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


import java.util.ArrayList;
import java.util.Collection;

/**
 * 飞书项目连接器配置类
 * 定义连接到飞书项目所需的基本参数
 */
public class FeishuConnection extends Connection {
    
    private String serverUrl;
    private String pluginToken;
    private String userKey;
    
    @Deprecated
    public FeishuConnection() {}
    
    /**
     * 构造函数
     * @param id 连接ID
     * @param serverUrl 服务器地址
     * @param pluginToken 插件Token
     * @param userKey 用户Key
     */
    public FeishuConnection(String id, String serverUrl, String pluginToken, String userKey) {
        super(id, userKey != null ? userKey : "", pluginToken != null ? pluginToken : "");
        this.serverUrl = serverUrl != null ? serverUrl : "https://project.feishu.cn";
        this.pluginToken = pluginToken;
        this.userKey = userKey;
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
    
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    public String getPluginToken() {
        return pluginToken;
    }

    public void setPluginToken(String pluginToken) {
        this.pluginToken = pluginToken;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }
    
    /**
     * 检查连接配置的有效性
     * @return 错误信息，如果配置有效则返回null
     */
    @Nullable
    public String check() {
        Collection<String> missing = new ArrayList<>();
        
        if (getServerUrl() == null || getServerUrl().isEmpty()) {
            missing.add("服务器地址");
        }
        
        if (getPluginToken() == null || getPluginToken().isEmpty()) {
            missing.add("插件Token");
        }
        
        if (!missing.isEmpty()) {
            return "缺少必需的配置项: " + String.join(", ", missing);
        }
        
        // 验证服务器地址格式
        if (!getServerUrl().startsWith("http://") && !getServerUrl().startsWith("https://")) {
            return "服务器地址必须以 http:// 或 https:// 开头";
        }
        
        return null;
    }
    
    /**
     * 获取完整的API基础URL
     * @return API基础URL
     */
    @NotNull
    public String getApiBaseUrl() {
        String baseUrl = getServerUrl();
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl + "/open_api";
    }
    
    /**
     * 测试连接是否可用
     * @return 连接测试结果
     */
    public boolean testConnection() {
        // TODO: 实现连接测试逻辑
        // 可以调用飞书项目的健康检查接口或获取用户信息接口
        return true;
    }
}
